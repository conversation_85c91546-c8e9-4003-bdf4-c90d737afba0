import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import { wrapLanguageModel, extractReasoningMiddleware } from "ai";
import type { LanguageModelV1 } from "ai";

// Model configuration types
export interface ModelConfig {
  primary: LanguageModelV1;
  fallback: LanguageModelV1;
  name: string;
}

// Error types that should trigger fallback
const GEMINI_FALLBACK_ERRORS = ["thought", "thinking", "type validation", "streaming", "unsupported response format", "invalid response structure"];

/**
 * Checks if an error should trigger a fallback to OpenAI
 */
export function shouldFallbackToOpenAI(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorString = error.toString?.()?.toLowerCase() || "";

  return GEMINI_FALLBACK_ERRORS.some((keyword) => errorMessage.includes(keyword) || errorString.includes(keyword));
}

/**
 * Creates Gemini models with proper thinking configuration
 */
export function createGeminiModels() {
  const geminiChatModel = google("gemini-2.5-flash-preview-04-17", {
    // Enable structured outputs for better compatibility
    structuredOutputs: true,
    // Configure safety settings to be less restrictive
    safetySettings: [
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
    ],
  });

  const geminiReasoningModel = wrapLanguageModel({
    model: google("gemini-2.5-flash-preview-04-17", {
      structuredOutputs: true,
      safetySettings: [
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
      ],
    }),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: geminiChatModel,
    reasoning: geminiReasoningModel,
    title: geminiChatModel,
    artifact: geminiChatModel,
  };
}

/**
 * Creates OpenAI models as fallback
 */
export function createOpenAIModels() {
  const openaiChatModel = openai("gpt-4o-mini");

  const openaiReasoningModel = wrapLanguageModel({
    model: openai("gpt-4o-mini"),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: openaiChatModel,
    reasoning: openaiReasoningModel,
    title: openaiChatModel,
    artifact: openaiChatModel,
  };
}

/**
 * Creates model configurations with primary (Gemini) and fallback (OpenAI) models
 */
export function createModelConfigs(): Record<string, ModelConfig> {
  const geminiModels = createGeminiModels();
  const openaiModels = createOpenAIModels();

  return {
    "chat-model": {
      primary: geminiModels.chat,
      fallback: openaiModels.chat,
      name: "chat-model",
    },
    "chat-model-reasoning": {
      primary: geminiModels.reasoning,
      fallback: openaiModels.reasoning,
      name: "chat-model-reasoning",
    },
    "title-model": {
      primary: geminiModels.title,
      fallback: openaiModels.title,
      name: "title-model",
    },
    "artifact-model": {
      primary: geminiModels.artifact,
      fallback: openaiModels.artifact,
      name: "artifact-model",
    },
  };
}

/**
 * Wrapper that provides automatic fallback from Gemini to OpenAI
 */
export class FallbackLanguageModel implements LanguageModelV1 {
  constructor(private config: ModelConfig, private onFallback?: (modelName: string, error: any) => void) {}

  get specificationVersion() {
    return this.config.primary.specificationVersion;
  }

  get defaultObjectGenerationMode() {
    return this.config.primary.defaultObjectGenerationMode;
  }

  get modelId() {
    return this.config.primary.modelId;
  }

  get provider() {
    return this.config.primary.provider;
  }

  get maxTokensInContext() {
    return this.config.primary.maxTokensInContext;
  }

  get maxTokensPerGeneration() {
    return this.config.primary.maxTokensPerGeneration;
  }

  get supportsImageInput() {
    return this.config.primary.supportsImageInput;
  }

  get supportsUrl() {
    return this.config.primary.supportsUrl;
  }

  async doGenerate(options: any) {
    try {
      console.log(`✨ [Gemini] Using ${this.config.name} with Gemini 2.5 Flash Preview`);
      const result = await this.config.primary.doGenerate(options);
      console.log(`✅ [Gemini] ${this.config.name} generation completed successfully`);
      return result;
    } catch (error) {
      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const result = await this.config.fallback.doGenerate(options);
        console.log(`✅ [OpenAI] ${this.config.name} generation completed with fallback`);
        return result;
      }
      throw error;
    }
  }

  async doStream(options: any) {
    try {
      console.log(`✨ [Gemini] Streaming ${this.config.name} with Gemini 2.5 Flash Preview`);
      const result = await this.config.primary.doStream(options);
      console.log(`✅ [Gemini] ${this.config.name} streaming started successfully`);
      return result;
    } catch (error) {
      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} streaming falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const result = await this.config.fallback.doStream(options);
        console.log(`✅ [OpenAI] ${this.config.name} streaming started with fallback`);
        return result;
      }
      throw error;
    }
  }
}

/**
 * Logs fallback events for monitoring
 */
export function logFallbackEvent(modelName: string, error: any) {
  const errorMessage = error.message || error.toString();
  console.warn(`🔄 [Model Fallback] ${modelName} → OpenAI: ${errorMessage}`);

  // Log additional details for debugging
  if (error.stack) {
    console.debug(`[Model Fallback Debug] ${modelName}:`, error.stack);
  }

  // In production, you might want to send this to your monitoring service
  // Example: analytics.track('model_fallback', { model: modelName, error: errorMessage });
}
