import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import { wrapLanguageModel, extractReasoningMiddleware } from "ai";
import type { LanguageModelV1 } from "ai";

// Model configuration types
export interface ModelConfig {
  primary: LanguageModelV1;
  fallback: LanguageModelV1;
  name: string;
}

// Error types that should trigger fallback
const GEMINI_FALLBACK_ERRORS = ["thought", "thinking", "type validation", "streaming", "unsupported response format", "invalid response structure"];

/**
 * Checks if an error should trigger a fallback to OpenAI
 */
export function shouldFallbackToOpenAI(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorString = error.toString?.()?.toLowerCase() || "";

  return GEMINI_FALLBACK_ERRORS.some((keyword) => errorMessage.includes(keyword) || errorString.includes(keyword));
}

/**
 * Creates Gemini models with proper thinking configuration
 */
export function createGeminiModels() {
  const geminiChatModel = google("gemini-2.5-flash-preview-04-17", {
    // Enable structured outputs for better compatibility
    structuredOutputs: true,
    // Configure safety settings to be less restrictive
    safetySettings: [
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
    ],
  });

  const geminiReasoningModel = wrapLanguageModel({
    model: google("gemini-2.5-flash-preview-04-17", {
      structuredOutputs: true,
      safetySettings: [
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
      ],
    }),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: geminiChatModel,
    reasoning: geminiReasoningModel,
    title: geminiChatModel,
    artifact: geminiChatModel,
  };
}

/**
 * Creates OpenAI models as fallback
 */
export function createOpenAIModels() {
  const openaiChatModel = openai("gpt-4o-mini");

  const openaiReasoningModel = wrapLanguageModel({
    model: openai("gpt-4o-mini"),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: openaiChatModel,
    reasoning: openaiReasoningModel,
    title: openaiChatModel,
    artifact: openaiChatModel,
  };
}

/**
 * Creates model configurations with primary (Gemini) and fallback (OpenAI) models
 */
export function createModelConfigs(): Record<string, ModelConfig> {
  const geminiModels = createGeminiModels();
  const openaiModels = createOpenAIModels();

  return {
    "chat-model": {
      primary: geminiModels.chat,
      fallback: openaiModels.chat,
      name: "chat-model",
    },
    "chat-model-reasoning": {
      primary: geminiModels.reasoning,
      fallback: openaiModels.reasoning,
      name: "chat-model-reasoning",
    },
    "title-model": {
      primary: geminiModels.title,
      fallback: openaiModels.title,
      name: "title-model",
    },
    "artifact-model": {
      primary: geminiModels.artifact,
      fallback: openaiModels.artifact,
      name: "artifact-model",
    },
  };
}

/**
 * Wrapper that provides automatic fallback from Gemini to OpenAI
 */
export class FallbackLanguageModel implements LanguageModelV1 {
  constructor(private config: ModelConfig, private onFallback?: (modelName: string, error: any) => void) {}

  get specificationVersion() {
    return this.config.primary.specificationVersion;
  }

  get defaultObjectGenerationMode() {
    return this.config.primary.defaultObjectGenerationMode;
  }

  get modelId() {
    return this.config.primary.modelId;
  }

  get provider() {
    return this.config.primary.provider;
  }

  get maxTokensInContext() {
    return this.config.primary.maxTokensInContext;
  }

  get maxTokensPerGeneration() {
    return this.config.primary.maxTokensPerGeneration;
  }

  get supportsImageInput() {
    return this.config.primary.supportsImageInput;
  }

  get supportsUrl() {
    return this.config.primary.supportsUrl;
  }

  async doGenerate(options: any) {
    const startTime = Date.now();
    try {
      console.log(`✨ [Gemini] Using ${this.config.name} with Gemini 2.5 Flash Preview`);
      console.log(`🔍 [Gemini Debug] Generation options:`, {
        prompt: options.prompt?.slice(0, 100) + "...",
        messages: options.messages?.length || 0,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
      });

      const result = await this.config.primary.doGenerate(options);
      const duration = Date.now() - startTime;

      // Log detailed response structure
      console.log(`📊 [Gemini Response] Generation completed in ${duration}ms`);
      console.log(`🔍 [Gemini Response Structure]:`, {
        text: result.text?.slice(0, 100) + "...",
        finishReason: result.finishReason,
        usage: result.usage,
        rawCall: result.rawCall ? "present" : "absent",
        warnings: result.warnings,
        reasoning: result.reasoning ? "present" : "absent",
        files: result.files?.length || 0,
      });

      // Check for thought properties in the raw response
      if (result.rawCall) {
        console.log(`🧠 [Gemini Raw Call] Available properties:`, Object.keys(result.rawCall));
        // Try to access the raw response through different possible paths
        const rawResponse = (result.rawCall as any).rawResponse || (result as any).rawResponse;
        if (rawResponse) {
          console.log(`🧠 [Gemini Thought Check] Raw response keys:`, Object.keys(rawResponse));
          if (rawResponse.thought || rawResponse.thinking) {
            console.log(`🧠 [Gemini Thought Found] Thought properties detected:`, {
              thought: !!rawResponse.thought,
              thinking: !!rawResponse.thinking,
            });
          }
        }

        // Also check the entire result object for thought properties
        const resultKeys = Object.keys(result);
        console.log(`🔍 [Gemini Result Keys]:`, resultKeys);
        if ((result as any).thought || (result as any).thinking) {
          console.log(`🧠 [Gemini Thought in Result] Thought properties found in main result:`, {
            thought: !!(result as any).thought,
            thinking: !!(result as any).thinking,
          });
        }
      }

      console.log(`✅ [Gemini] ${this.config.name} generation completed successfully`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorInfo =
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack?.split("\n").slice(0, 3),
              cause: (error as any).cause,
            }
          : { error: String(error) };

      console.error(`❌ [Gemini Error] Generation failed after ${duration}ms:`, errorInfo);

      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const fallbackStartTime = Date.now();
        const result = await this.config.fallback.doGenerate(options);
        const fallbackDuration = Date.now() - fallbackStartTime;
        console.log(`✅ [OpenAI] ${this.config.name} generation completed with fallback in ${fallbackDuration}ms`);
        return result;
      }
      throw error;
    }
  }

  async doStream(options: any) {
    const startTime = Date.now();
    try {
      console.log(`✨ [Gemini] Streaming ${this.config.name} with Gemini 2.5 Flash Preview`);
      console.log(`🔍 [Gemini Stream Debug] Options:`, {
        messages: options.messages?.length || 0,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        tools: options.tools ? Object.keys(options.tools).length : 0,
        providerOptions: options.providerOptions ? "present" : "absent",
      });

      const result = await this.config.primary.doStream(options);
      const duration = Date.now() - startTime;

      console.log(`📊 [Gemini Stream] Stream created in ${duration}ms`);
      console.log(`🔍 [Gemini Stream Structure]:`, {
        stream: result.stream ? "present" : "absent",
        rawCall: result.rawCall ? "present" : "absent",
        warnings: result.warnings,
      });

      // Wrap the stream to log chunks and detect thought properties
      if (result.stream) {
        const originalStream = result.stream;
        let chunkCount = 0;
        let thoughtDetected = false;

        const wrappedStream = new ReadableStream({
          start(controller) {
            console.log(`🌊 [Gemini Stream] Stream started, monitoring for thought properties...`);
          },
          async pull(controller) {
            try {
              const reader = originalStream.getReader();
              const { done, value } = await reader.read();

              if (done) {
                console.log(`🏁 [Gemini Stream] Stream completed after ${chunkCount} chunks`);
                if (thoughtDetected) {
                  console.log(`🧠 [Gemini Stream] Thought properties were detected during streaming`);
                }
                controller.close();
                return;
              }

              chunkCount++;

              // Log chunk details
              if (chunkCount <= 5 || chunkCount % 10 === 0) {
                const valueAny = value as any;
                console.log(`📦 [Gemini Chunk ${chunkCount}]:`, {
                  type: value?.type,
                  textDelta: value?.type === "text-delta" ? "present" : "absent",
                  toolCall: value?.type === "tool-call" ? "present" : "absent",
                  thought: valueAny?.thought ? "present" : "absent",
                  thinking: valueAny?.thinking ? "present" : "absent",
                  // Log all available properties
                  allKeys: Object.keys(valueAny || {}),
                });
              }

              // Check for thought properties in the chunk
              const valueAny = value as any;
              if (value && (valueAny.thought || valueAny.thinking)) {
                thoughtDetected = true;
                console.log(`🧠 [Gemini Thought Chunk] Thought detected in chunk ${chunkCount}:`, {
                  thought: !!valueAny.thought,
                  thinking: !!valueAny.thinking,
                  thoughtContent: valueAny.thought,
                  thinkingContent: valueAny.thinking,
                });
              }

              controller.enqueue(value);
              reader.releaseLock();
            } catch (streamError) {
              console.error(`❌ [Gemini Stream Error] Error in chunk ${chunkCount}:`, streamError);
              controller.error(streamError);
            }
          },
        });

        // Return the result with the wrapped stream
        return {
          ...result,
          stream: wrappedStream,
        };
      }

      console.log(`✅ [Gemini] ${this.config.name} streaming started successfully`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorInfo =
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack?.split("\n").slice(0, 3),
            }
          : { error: String(error) };

      console.error(`❌ [Gemini Stream Error] Streaming failed after ${duration}ms:`, errorInfo);

      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} streaming falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const fallbackStartTime = Date.now();
        const result = await this.config.fallback.doStream(options);
        const fallbackDuration = Date.now() - fallbackStartTime;
        console.log(`✅ [OpenAI] ${this.config.name} streaming started with fallback in ${fallbackDuration}ms`);
        return result;
      }
      throw error;
    }
  }
}

/**
 * Logs fallback events for monitoring
 */
export function logFallbackEvent(modelName: string, error: any) {
  const errorMessage = error.message || error.toString();
  console.warn(`🔄 [Model Fallback] ${modelName} → OpenAI: ${errorMessage}`);

  // Log additional details for debugging
  if (error.stack) {
    console.debug(`[Model Fallback Debug] ${modelName}:`, error.stack);
  }

  // In production, you might want to send this to your monitoring service
  // Example: analytics.track('model_fallback', { model: modelName, error: errorMessage });
}
