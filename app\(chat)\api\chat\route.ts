import { appendClientMessage, appendResponseMessages, createDataStream, smoothStream, streamText } from "ai";
import { getServerSession } from "@/app/(auth)/auth-server";
import type { UserType } from "@/app/(auth)/auth";
import { type RequestHints, systemPrompt } from "@/lib/ai/prompts";
import { adaptSupabaseSession } from "@/lib/auth/session-adapter";
import { validateAndDeductCredits, CREDIT_COSTS, createTransactionDescription } from "@/lib/auth/credit-middleware";
import {
  createStreamId,
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  getStreamIdsByChatId,
  saveChat,
  saveMessages,
} from "@/lib/db/queries";
import { generateUUID, getTrailingMessageId } from "@/lib/utils";
import { generateTitleFromUserMessage } from "../../actions";
import { createDocument } from "@/lib/ai/tools/create-document";
import { updateDocument } from "@/lib/ai/tools/update-document";
import { requestSuggestions } from "@/lib/ai/tools/request-suggestions";
import { getWeather } from "@/lib/ai/tools/get-weather";
import { testTool } from "@/lib/ai/tools/test-tool";
import { checkBatTuTool } from "@/lib/ai/tools/check-bat-tu";
import { checkDetailedPersonalityTool } from "@/lib/ai/tools/check-detailed-personality";
import { checkLunarDateTool } from "@/lib/ai/tools/check-lunar-date";
import { checkPersonalityTool } from "@/lib/ai/tools/get-personality";
import { getCurrentDateTimeTool } from "@/lib/ai/tools/get-current-date-time";
import { getDaiVanTool } from "@/lib/ai/tools/check-dai-van";
import { isProductionEnvironment } from "@/lib/constants";
import { myProvider } from "@/lib/ai/providers";
import { entitlementsByUserType } from "@/lib/ai/entitlements";
import { mcpLogger } from "@/lib/ai/mcp-client";
import { postRequestBodySchema, type PostRequestBody } from "./schema";
import { trackToolCall, trackToolResult } from "@/lib/analytics/server";
import { getToolCallKey, startToolCallTiming, endToolCallTiming } from "@/lib/analytics/timing";
import { geolocation } from "@vercel/functions";
import { getStreamContext, resetStreamContext } from "@/lib/ai/stream-context";
import type { Chat } from "@/lib/db/schema";
import { differenceInSeconds } from "date-fns";
import { ChatSDKError } from "@/lib/errors";
import { checkRedisHealthWithSharedClient } from "@/lib/redis/client";

export const maxDuration = 90;
// Global timeout for chat requests in milliseconds (60 seconds)
const CHAT_REQUEST_TIMEOUT = 60000;

// Helper function to handle the chat request with timeout
async function handleChatRequest(request: Request): Promise<Response> {
  let requestBody: PostRequestBody;

  try {
    const json = await request.json();
    requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new ChatSDKError("bad_request:api").toResponse();
  }

  try {
    const { id, message, selectedChatModel, selectedVisibilityType } = requestBody;

    const session = await getServerSession();

    if (!session?.user) {
      return new ChatSDKError("unauthorized:chat").toResponse();
    }

    const userType: UserType = session.user.type;

    const messageCount = await getMessageCountByUserId({
      id: session.user.id,
      differenceInHours: 24,
    });

    if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
      return new ChatSDKError("rate_limit:chat").toResponse();
    }

    // Credit deduction - fail-fast approach: deduct credits before processing
    try {
      const messagePreview = typeof message.parts[0]?.text === "string" ? message.parts[0].text : "Message";

      const transactionDescription = createTransactionDescription.chatMessage(messagePreview);

      await validateAndDeductCredits(CREDIT_COSTS.CHAT_MESSAGE, transactionDescription);

      console.log(`[CREDIT] Successfully deducted ${CREDIT_COSTS.CHAT_MESSAGE} credit(s) for user ${session.user.id}`);
    } catch (error) {
      // Credit deduction failed - return error response immediately
      if (error instanceof ChatSDKError) {
        console.log(`[CREDIT] Credit deduction failed for user ${session.user.id}: ${error.message}`);
        return error.toResponse();
      }

      console.error(`[CREDIT] Unexpected error during credit deduction:`, error);
      return new ChatSDKError("forbidden:credit", "Failed to process credit deduction").toResponse();
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message,
      });

      await saveChat({
        id,
        userId: session.user.id,
        title,
        visibility: selectedVisibilityType,
      });
    } else {
      if (chat.userId !== session.user.id) {
        return new ChatSDKError("forbidden:chat").toResponse();
      }
    }

    const previousMessages = await getMessagesByChatId({ id });

    const messages = appendClientMessage({
      // @ts-expect-error: todo add type conversion from DBMessage[] to UIMessage[]
      messages: previousMessages,
      message,
    });

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: "user",
          parts: message.parts,
          attachments: message.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    const stream = createDataStream({
      execute: async (dataStream) => {
        // MCP server is temporarily disabled
        const mcpTools = {};
        mcpLogger.info("MCP server is temporarily disabled");

        // Log the active tools before calling streamText
        const activeTools =
          selectedChatModel === "chat-model-reasoning"
            ? []
            : [
                "checkBatTuTool",
                "checkLunarDateTool",
                "checkDetailedPersonalityTool",
                "getDaiVanTool",
                "checkPersonalityTool",
                "getCurrentDateTimeTool",
              ];

        mcpLogger.info(`Setting up streamText with active tools: ${activeTools.join(", ")}`);

        mcpLogger.info("Starting streamText operation");

        let result: any;
        try {
          // Create a streamText configuration object with all the properties
          const streamTextConfig = {
            model: myProvider.languageModel(selectedChatModel),
            system: systemPrompt({ selectedChatModel, requestHints }),
            temperature: 0.6,
            messages,
            maxSteps: 5,
            experimental_activeTools: activeTools,
            experimental_transform: smoothStream({ chunking: "word" }),
            experimental_generateMessageId: generateUUID,
            tools: {
              // Local tools
              getWeather,
              createDocument: createDocument({ session: adaptSupabaseSession(session), dataStream }),
              updateDocument: updateDocument({ session: adaptSupabaseSession(session), dataStream }),
              requestSuggestions: requestSuggestions({
                session: adaptSupabaseSession(session),
                dataStream,
              }),
              // Test tool for debugging
              testTool,
              checkBatTuTool,
              checkLunarDateTool,
              checkDetailedPersonalityTool,
              getDaiVanTool,
              checkPersonalityTool,
              getCurrentDateTimeTool,
              // MCP tools
              // ...mcpTools,
            },
            experimental_telemetry: {
              isEnabled: isProductionEnvironment,
              functionId: "stream-text",
            },
          };

          // Add the callbacks using type assertion
          const configWithCallbacks = {
            ...streamTextConfig,
            // Add tool call callback
            onToolCall: ({ toolName, args }: { toolName: string; args: Record<string, unknown> }) => {
              console.log(`[TOOL CALL] Tool called: ${toolName}`);
              mcpLogger.info(`Tool called: ${toolName}`, { args });

              // Generate a unique key for this tool call
              const toolCallKey = getToolCallKey(id, toolName);

              // Start timing the tool call
              startToolCallTiming(toolCallKey);

              // Store the tool call key in a property that will be accessible in the result callback
              (args as any).__toolCallKey = toolCallKey;

              // Track tool call in PostHog
              if (session.user?.id) {
                // Pass the user ID, chat ID, tool name, and args to the tracking function
                trackToolCall(session.user.id, id, toolName, args);
              }

              // Check if it's an MCP tool
              if (Object.keys(mcpTools).includes(toolName)) {
                console.log(`[MCP TOOL CALL] MCP tool called: ${toolName}`);
              }
            },
            // Add tool result callback
            onToolResult: ({ toolName, args, result }: { toolName: string; args: Record<string, unknown>; result: unknown }) => {
              console.log(`[TOOL RESULT] Tool result received: ${toolName}`);
              mcpLogger.info(`Tool result received: ${toolName}`, { args, result });

              // Get the tool call key from the args
              const toolCallKey = (args as any).__toolCallKey;

              // Calculate the duration of the tool call
              const durationMs = toolCallKey ? endToolCallTiming(toolCallKey) : 0;

              // Track tool result in PostHog
              if (session.user?.id) {
                // Assume success if we have a result
                const success = !!result;

                // Pass the user ID, chat ID, tool name, success status, duration, and result to the tracking function
                trackToolResult(session.user.id, id, toolName, success, durationMs, result);
              }

              // Check if it's an MCP tool
              if (Object.keys(mcpTools).includes(toolName)) {
                console.log(`[MCP TOOL RESULT] MCP tool result received: ${toolName}`);
              }

              // Send a thinking indicator to show the LLM is processing the tool result
              dataStream.writeData({
                type: "thinking-after-tool",
                content: toolName,
              });
            },
            // Add finish callback
            onFinish: async ({ response, steps }: any) => {
              console.log(`[FINISH] Stream finished processing`);
              mcpLogger.info("Stream finished processing");

              // Log tool usage statistics
              const toolCalls = steps.flatMap((step: any) => step.toolCalls || []);
              const toolResults = steps.flatMap((step: any) => step.toolResults || []);

              console.log(`[STATS] Total steps: ${steps.length}, Tool calls: ${toolCalls.length}, Tool results: ${toolResults.length}`);

              if (toolCalls.length > 0) {
                console.log(`[TOOL CALLS] ${toolCalls.map((call: any) => call.toolName).join(", ")}`);
                mcpLogger.info(
                  `Total tool calls: ${toolCalls.length}`,
                  toolCalls.map((call: any) => ({
                    toolName: call.toolName,
                    args: call.args,
                  }))
                );
              }

              if (toolResults.length > 0) {
                console.log(`[TOOL RESULTS] ${toolResults.map((result: any) => result.toolName).join(", ")}`);
                mcpLogger.info(
                  `Total tool results: ${toolResults.length}`,
                  toolResults.map((result: any) => ({
                    toolName: result.toolName,
                  }))
                );
              }

              if (session.user?.id) {
                try {
                  const assistantId = getTrailingMessageId({
                    messages: response.messages.filter((message: any) => message.role === "assistant"),
                  });

                  if (!assistantId) {
                    throw new Error("No assistant message found!");
                  }

                  const [, assistantMessage] = appendResponseMessages({
                    messages: [message],
                    responseMessages: response.messages,
                  });

                  await saveMessages({
                    messages: [
                      {
                        id: assistantId,
                        chatId: id,
                        role: assistantMessage.role,
                        parts: assistantMessage.parts,
                        attachments: assistantMessage.experimental_attachments ?? [],
                        createdAt: new Date(),
                      },
                    ],
                  });
                } catch (_) {
                  mcpLogger.error("Failed to save chat");
                }
              }
            },
          };

          // Call streamText with the configuration
          result = streamText(configWithCallbacks as any);

          // Log that the stream was created
          console.log(`[STREAM] Stream created successfully`);
          mcpLogger.info("Stream created successfully");

          // Note: The streamText function doesn't support event listeners directly
          // Tool calls and results will be logged via the onToolCall and onFinish callbacks
        } catch (error) {
          console.error("[STREAM ERROR] Error creating streamText:", error);
          mcpLogger.error("Error creating streamText:", error);
          throw error;
        }

        try {
          console.log(`[STREAM] About to consume stream`);
          mcpLogger.info("About to consume stream");
          result.consumeStream();
          console.log(`[STREAM] Stream consumption started`);
          mcpLogger.info("Stream consumption started");
        } catch (error) {
          console.error("[STREAM ERROR] Error consuming stream:", error);
          mcpLogger.error("Error consuming stream:", error);
          throw error;
        }

        try {
          console.log(`[STREAM] About to merge into data stream`);
          mcpLogger.info("About to merge into data stream");
          result.mergeIntoDataStream(dataStream, {
            sendReasoning: true,
          });
          console.log(`[STREAM] Stream merged into data stream`);
          mcpLogger.info("Stream merged into data stream");
        } catch (error) {
          console.error("[STREAM ERROR] Error merging into data stream:", error);
          mcpLogger.error("Error merging into data stream:", error);
          throw error;
        }
      },
      onError: (error) => {
        mcpLogger.error("Error in data stream execution:", error);

        // Check if it's a timeout error from our MCP tool wrappers
        if (error instanceof Error && error.message.includes("timed out")) {
          return "I'm sorry, but the request to an external tool timed out. This might be due to connectivity issues or the tool taking too long to respond. Please try again or try a different approach.";
        }

        return "I apologize, but I encountered an error while processing your request. This might be due to a temporary issue with external tools. Please try again in a moment.";
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(await streamContext.resumableStream(streamId, () => stream));
    } else {
      return new Response(stream);
    }
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error("Unhandled error in chat route:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function POST(request: Request) {
  // Create a timeout promise
  const timeoutPromise = new Promise<Response>((_, reject) => {
    setTimeout(() => {
      // Reset the stream context when a timeout occurs
      resetStreamContext();
      reject(new Error("Request timed out"));
    }, CHAT_REQUEST_TIMEOUT);
  });

  try {
    // Check Redis health before proceeding
    const redisHealthy = await checkRedisHealthWithSharedClient();
    if (!redisHealthy) {
      // Reset the stream context if Redis is unhealthy
      resetStreamContext();
      console.warn("Redis connection unhealthy, disabled resumable streams");
    }

    // Race the actual request handling against the timeout
    return await Promise.race([handleChatRequest(request.clone()), timeoutPromise]);
  } catch (error: any) {
    console.error("Error in POST handler:", error);

    if (error.message === "Request timed out") {
      console.error("Chat request timed out after", CHAT_REQUEST_TIMEOUT, "ms");
      return new Response(JSON.stringify({ error: "Request timed out" }), {
        status: 504,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Handle other errors
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

export async function GET(request: Request) {
  // Check Redis health before proceeding
  const redisHealthy = await checkRedisHealthWithSharedClient();
  if (!redisHealthy) {
    // Reset the stream context if Redis is unhealthy
    resetStreamContext();
    console.warn("Redis connection unhealthy, disabled resumable streams");
    return new Response(null, { status: 204 });
  }

  const streamContext = getStreamContext();
  const resumeRequestedAt = new Date();

  if (!streamContext) {
    return new Response(null, { status: 204 });
  }

  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get("chatId");

  if (!chatId) {
    return new ChatSDKError("bad_request:api").toResponse();
  }

  const session = await getServerSession();

  if (!session?.user) {
    return new ChatSDKError("unauthorized:chat").toResponse();
  }

  let chat: Chat;

  try {
    chat = await getChatById({ id: chatId });
  } catch {
    return new ChatSDKError("not_found:chat").toResponse();
  }

  if (!chat) {
    return new ChatSDKError("not_found:chat").toResponse();
  }

  if (chat.visibility === "private" && chat.userId !== session.user.id) {
    return new ChatSDKError("forbidden:chat").toResponse();
  }

  const streamIds = await getStreamIdsByChatId({ chatId });

  if (!streamIds.length) {
    return new ChatSDKError("not_found:stream").toResponse();
  }

  const recentStreamId = streamIds.at(-1);

  if (!recentStreamId) {
    return new ChatSDKError("not_found:stream").toResponse();
  }

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  const stream = await streamContext.resumableStream(recentStreamId, () => emptyDataStream);

  /*
   * For when the generation is streaming during SSR
   * but the resumable stream has concluded at this point.
   */
  if (!stream) {
    const messages = await getMessagesByChatId({ id: chatId });
    const mostRecentMessage = messages.at(-1);

    if (!mostRecentMessage) {
      return new Response(emptyDataStream, { status: 200 });
    }

    if (mostRecentMessage.role !== "assistant") {
      return new Response(emptyDataStream, { status: 200 });
    }

    const messageCreatedAt = new Date(mostRecentMessage.createdAt);

    if (differenceInSeconds(resumeRequestedAt, messageCreatedAt) > 15) {
      return new Response(emptyDataStream, { status: 200 });
    }

    const restoredStream = createDataStream({
      execute: (buffer) => {
        buffer.writeData({
          type: "append-message",
          message: JSON.stringify(mostRecentMessage),
        });
      },
    });

    return new Response(restoredStream, { status: 200 });
  }

  return new Response(stream, { status: 200 });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new ChatSDKError("bad_request:api").toResponse();
  }

  const session = await getServerSession();

  if (!session?.user) {
    return new ChatSDKError("unauthorized:chat").toResponse();
  }

  const chat = await getChatById({ id });

  if (chat.userId !== session.user.id) {
    return new ChatSDKError("forbidden:chat").toResponse();
  }

  const deletedChat = await deleteChatById({ id });

  return Response.json(deletedChat, { status: 200 });
}
