import { customProvider, extractReasoningMiddleware, wrapLanguageModel } from "ai";
import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import { isTestEnvironment } from "../constants";
import { artifactModel, chatModel, reasoningModel, titleModel } from "./models.test";
import { type createMCPClientWithTools, mcpLogger } from "./mcp-client";

// MCP server configuration
export const MCP_SERVER_URL = process.env.MCP_SERVER_URL || "https://your-mcp-server.com/mcp";
export const MCP_API_KEY = process.env.MCP_API_KEY;

// Cache for MCP client and tools
const mcpClientCache: Awaited<ReturnType<typeof createMCPClientWithTools>> | null = null;

/**
 * Gets MCP tools from the server
 * @returns MCP tools that can be used with the AI model
 */
export async function getMCPTools() {
  mcpLogger.info("MCP server is temporarily disabled");

  // MCP server is temporarily disabled
  // To re-enable, uncomment the code below and comment out the return statement
  return {};

  /*
  mcpLogger.info("Getting MCP tools");

  if (!mcpClientCache) {
    mcpLogger.info("No MCP client cache found, initializing new client");
    try {
      mcpClientCache = await createMCPClientWithTools(MCP_SERVER_URL, MCP_API_KEY);
      mcpLogger.info("MCP client cache created successfully");

      // Set up cleanup on process exit
      process.on("beforeExit", async () => {
        mcpLogger.info("Process exiting, cleaning up MCP client");
        if (mcpClientCache?.mcpClient) {
          await closeMCPClient(mcpClientCache.mcpClient);
          mcpClientCache = null;
          mcpLogger.info("MCP client cache cleared");
        }
      });
    } catch (error) {
      mcpLogger.error("Failed to initialize MCP client", error);
      return {};
    }
  } else {
    mcpLogger.info("Using existing MCP client cache");
  }

  const tools = mcpClientCache.tools;
  const toolNames = Object.keys(tools);

  mcpLogger.info(`Retrieved ${toolNames.length} MCP tools: ${toolNames.join(", ")}`);

  return tools;
  */
}

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        "chat-model": chatModel,
        "chat-model-reasoning": reasoningModel,
        "title-model": titleModel,
        "artifact-model": artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        "chat-model": openai("gpt-4.1-mini-2025-04-14"),
        "chat-model-reasoning": wrapLanguageModel({
          model: openai("gpt-4.1-mini-2025-04-14"),
          middleware: extractReasoningMiddleware({ tagName: "think" }),
        }),
        "title-model": openai("gpt-4.1-mini-2025-04-14"),
        "artifact-model": openai("gpt-4.1-mini-2025-04-14"),
      },
      // For image generation, we'll use the experimental_generateImage function
      // Note: OpenAI models are used for text generation, image generation may need separate configuration
      imageModels: {},
    });
